export class CostCenterAllocationResponseDto {
  id: number;
  centroCustoId: number;
  centroCustoNome: string;
  valor: number;
  porcentagem?: number;
}

export class LancamentoFinanceiroResponseDto {
  id: number;
  descricao: string;
  dataLancamento?: Date;
  dataCompetencia?: Date;
  valorBruto?: number;
  valor: number; // Valor líquido descriptografado
  observacao?: string;
  efetivado?: boolean;
  conciliado?: boolean;

  // Relacionamentos
  empresaId: number;
  pessoaId: number;
  pessoaNome?: string;
  contaId?: number;
  contaNome?: string;
  localId?: number;
  localNome?: string;
  fornecedorId?: number;
  fornecedorNome?: string;
  tipoLancamentoFinanceiroId: number;
  tipoLancamento?: string; // 'receita' ou 'despesa' para diferenciação visual
  categoriaLctoFinanceiroId?: number;
  categoriaNome?: string;
  planoContaCredito?: number;
  planoContaCreditoNome?: string;

  // Alocações de centro de custo
  alocacoesCentroCusto?: CostCenterAllocationResponseDto[];

  // Campos de auditoria
  dataHoraUsuarioInc: Date;
  dataHoraUsuarioAlt: Date;
  usuarioInc: string;
  usuarioAlt: string;
}
