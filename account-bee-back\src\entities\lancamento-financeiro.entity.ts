import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { Empresa } from './empresa.entity';
import { Pessoa } from './pessoa.entity';
import { Conta } from './conta.entity';
import { Local } from './local.entity';
import { Fornecedor } from './fornecedor.entity';
import { TipoLancamentoFinanceiro } from './tipo-lancamento-financeiro.entity';
import { CategoriaLancamento } from './categoria-lancamento.entity';
import { PlanoConta } from './plano-conta.entity';
import { FinanceiroCentroCusto } from './financeiro-centro-custo.entity';

@Entity({ name: 'LANCAMENTO_FINANCEIRO' })
export class LancamentoFinanceiro {
  @PrimaryGeneratedColumn({ name: 'ID' })
  id: number;

  @Column({ name: 'DESCRICAO', length: 200, nullable: false })
  descricao: string;

  @Column({ name: 'DATA_LANCAMENTO', type: 'date', nullable: true })
  dataLancamento?: Date;

  @Column({ name: 'DATA_COMPETENCIA', type: 'date', nullable: true })
  dataCompetencia?: Date;

  @Column({ name: 'VALOR_BRUTO', length: 500, nullable: true })
  valorBruto?: string; // Criptografado

  @Column({ name: 'VALOR', length: 500, nullable: true })
  valor?: string; // Criptografado (valor líquido)

  @Column({ name: 'OBSERVACAO', length: 1000, nullable: true })
  observacao?: string;

  @Column({ name: 'EFETIVADO', nullable: true })
  efetivado?: boolean;

  @Column({ name: 'CONCILIADO', nullable: true })
  conciliado?: boolean;

  @Column({ name: 'ID_TRASACAO_BANCARIA', length: 1000, nullable: true })
  idTransacaoBancaria?: string;

  @Column({ name: 'IDENTIFICADOR', length: 200, nullable: true })
  identificador?: string;

  // Foreign Keys
  @Column({ name: 'EMPRESA_ID', nullable: false })
  empresaId: number;

  @Column({ name: 'PESSOA_ID', nullable: false })
  pessoaId: number;

  @Column({ name: 'CONTA_ID', nullable: true })
  contaId?: number;

  @Column({ name: 'LOCAL_ID', nullable: true })
  localId?: number;

  @Column({ name: 'FORNECEDOR', nullable: true })
  fornecedorId?: number;

  @Column({ name: 'TIPO_LANCAMENTO_FINANCEIRO_ID', nullable: false })
  tipoLancamentoFinanceiroId: number;

  @Column({ name: 'CATEGORIA_LCTO_FINANCEIRO_ID', nullable: true })
  categoriaLctoFinanceiroId?: number;

  @Column({ name: 'PLANO_CONTA_CREDITO', nullable: true })
  planoContaCredito?: number;

  @Column({ name: 'PLANO_CONTA_DEBITO', nullable: true })
  planoContaDebito?: number;

  // Relacionamentos
  @ManyToOne(() => Empresa)
  @JoinColumn({ name: 'EMPRESA_ID' })
  empresa: Empresa;

  @ManyToOne(() => Pessoa)
  @JoinColumn({ name: 'PESSOA_ID' })
  pessoa: Pessoa;

  @ManyToOne(() => Conta)
  @JoinColumn({ name: 'CONTA_ID' })
  conta?: Conta;

  @ManyToOne(() => Local)
  @JoinColumn({ name: 'LOCAL_ID' })
  local?: Local;

  @ManyToOne(() => Fornecedor)
  @JoinColumn({ name: 'FORNECEDOR' })
  fornecedor?: Fornecedor;

  @ManyToOne(() => TipoLancamentoFinanceiro)
  @JoinColumn({ name: 'TIPO_LANCAMENTO_FINANCEIRO_ID' })
  tipoLancamentoFinanceiro: TipoLancamentoFinanceiro;

  @ManyToOne(() => CategoriaLancamento)
  @JoinColumn({ name: 'CATEGORIA_LCTO_FINANCEIRO_ID' })
  categoriaLancamentoFinanceiro?: CategoriaLancamento;

  @ManyToOne(() => PlanoConta)
  @JoinColumn({ name: 'PLANO_CONTA_CREDITO' })
  planoContaCreditoEntity?: PlanoConta;

  @ManyToOne(() => PlanoConta)
  @JoinColumn({ name: 'PLANO_CONTA_DEBITO' })
  planoContaDebitoEntity?: PlanoConta;

  @OneToMany(() => FinanceiroCentroCusto, financeiroCentroCusto => financeiroCentroCusto.lancamentoFinanceiro)
  financeiroCentroCustos: FinanceiroCentroCusto[];

  // Campos de auditoria
  @Column({
    name: 'DATA_HORA_USUARIO_ALT',
    type: 'datetime',
    precision: 6,
    nullable: false,
  })
  dataHoraUsuarioAlt: Date;

  @Column({
    name: 'DATA_HORA_USUARIO_DEL',
    type: 'datetime',
    precision: 6,
    nullable: true,
  })
  dataHoraUsuarioDel?: Date;

  @Column({
    name: 'DATA_HORA_USUARIO_INC',
    type: 'datetime',
    precision: 6,
    nullable: false,
  })
  dataHoraUsuarioInc: Date;

  @Column({ name: 'DATA_SYNC', type: 'datetime', precision: 6, nullable: true })
  dataSync?: Date;

  @Column({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' })
  isExcluido: string;

  @Column({ name: 'USUARIO_ALT', length: 500, nullable: false })
  usuarioAlt: string;

  @Column({ name: 'USUARIO_DEL', length: 500, nullable: true })
  usuarioDel?: string;

  @Column({ name: 'USUARIO_INC', length: 500, nullable: false })
  usuarioInc: string;

  @Column({ name: 'UUID', length: 50, nullable: true })
  uuid?: string;
}
