import { useState, useEffect, useCallback, useMemo } from 'react';
import { Plus, Edit, Check, Calendar, AlertTriangle, ChevronLeft, ChevronRight, X, Filter, Copy } from 'lucide-react';
import { Layout } from '@/components/layout';
import { Button, BulkActionsToolbar } from '@/components/ui/forms';
import { Card } from '@/components/ui/layout';
import { Badge } from '@/components/ui/shadcn/badge';
import { Checkbox } from '@/components/ui/forms';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/shadcn/table';
import { TablePagination } from '@/components/ui/navigation/TablePagination/TablePagination';
import { useAccountsPayable } from '@/hooks/useAccountsPayable';
import { useAccountsReceivable } from '@/hooks/useAccountsReceivable';
import { AccountPayableFormDialog } from '@/pages/AccountsPayable/components/AccountPayableFormDialog';
import { AccountReceivableFormDialog } from '@/pages/AccountsReceivable/components/AccountReceivableFormDialog';
import { ReceitaFormDialog } from './components/ReceitaFormDialog';
import { DespesaFormDialog } from './components/DespesaFormDialog';
import { useLancamentosFinanceiros } from '@/hooks/useLancamentosFinanceiros';
import { useToast } from '@/hooks/use-toast';
import { AccountPayable, AccountReceivable } from '@/types/accounts';
import { AdvancedFiltersModal, AdvancedFiltersData } from '@/components/ui/forms/AdvancedFiltersModal';
import { exportToExcel } from '@/utils/exportUtils';

const MONTHS = [
  'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
  'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
];

export default function Financial() {
  const { accounts: payableAccounts, loading: payableLoading, markAsPaid, markAsUnpaid, updateMultipleStatus: updateMultiplePayableStatus, deleteMultipleAccounts: deleteMultiplePayableAccounts } = useAccountsPayable();
  const { accounts: receivableAccounts, loading: receivableLoading, markAsNotReceived, updateMultipleStatus: updateMultipleReceivableStatus, deleteMultipleAccounts: deleteMultipleReceivableAccounts } = useAccountsReceivable();
  const {
    listarLancamentos,
    calcularValores,
    listarExtrato,
    alterarStatusEfetivado,
    clonarLancamento,
    loading: lancamentosLoading
  } = useLancamentosFinanceiros();
  const { toast } = useToast();

  // Estados para os novos dados
  const [despesas, setDespesas] = useState<any[]>([]);
  const [receitas, setReceitas] = useState<any[]>([]);
  const [extrato, setExtrato] = useState<any[]>([]);
  const [totaisDespesas, setTotaisDespesas] = useState<any>({});
  const [totaisReceitas, setTotaisReceitas] = useState<any>({});

  const [activeTab, setActiveTab] = useState<'pagar' | 'receber' | 'extrato'>('pagar');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFiltersData>({});
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  
  // Modal states
  const [isCreateDespesaOpen, setIsCreateDespesaOpen] = useState(false);
  const [isCreateReceitaOpen, setIsCreateReceitaOpen] = useState(false);
  const [editingPayable, setEditingPayable] = useState<AccountPayable | null>(null);
  const [editingReceivable, setEditingReceivable] = useState<AccountReceivable | null>(null);
  
  // Clone states
  const [despesaClonada, setDespesaClonada] = useState<any | null>(null);
  const [receitaClonada, setReceitaClonada] = useState<any | null>(null);

  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Função memoizada para carregar dados
  const carregarDados = useCallback(async () => {
    try {
      const mes = currentMonth + 1; // JavaScript months are 0-based
      const ano = currentYear;

      console.log('🔍 Frontend - Carregando dados:', { mes, ano });

      // Carregar despesas
      console.log('🔍 Frontend - Carregando despesas...');
      const dadosDespesas = await listarLancamentos('despesas', { mes, ano, page: 1, limit: 1000 });
      console.log('✅ Frontend - Despesas carregadas:', dadosDespesas);

      console.log('🔍 Frontend - Calculando totais de despesas...');
      const totaisDespesasData = await calcularValores('despesas', { mes, ano });
      console.log('✅ Frontend - Totais de despesas calculados:', totaisDespesasData);

      // Carregar receitas
      console.log('🔍 Frontend - Carregando receitas...');
      const dadosReceitas = await listarLancamentos('receitas', { mes, ano, page: 1, limit: 1000 });
      console.log('✅ Frontend - Receitas carregadas:', dadosReceitas);

      console.log('🔍 Frontend - Calculando totais de receitas...');
      const totaisReceitasData = await calcularValores('receitas', { mes, ano });
      console.log('✅ Frontend - Totais de receitas calculados:', totaisReceitasData);

      // Carregar extrato unificado
      console.log('🔍 Frontend - Carregando extrato...');
      const dadosExtrato = await listarExtrato({ mes, ano, page: 1, limit: 1000 });
      console.log('✅ Frontend - Extrato carregado:', dadosExtrato);

      setDespesas(dadosDespesas.data || []);
      setReceitas(dadosReceitas.data || []);
      setExtrato(dadosExtrato.data || []);
      setTotaisDespesas(totaisDespesasData);
      setTotaisReceitas(totaisReceitasData);

      console.log('✅ Frontend - Todos os dados carregados com sucesso');
    } catch (error) {
      console.error('❌ Frontend - Erro detalhado ao carregar dados:', error);
      console.error('❌ Frontend - Stack trace:', error instanceof Error ? error.stack : 'N/A');

      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

      toast({
        title: 'Erro ao carregar dados financeiros',
        description: `Detalhes: ${errorMessage}`,
        variant: 'destructive',
      });
    }
  }, [currentMonth, currentYear, listarLancamentos, calcularValores, toast]);

  // Carregar dados dos lançamentos financeiros
  useEffect(() => {
    carregarDados();
  }, [carregarDados]);

  // Mapear dados dos lançamentos para o formato da tabela
  const mapearLancamentoParaTabela = (lancamento: any, tipoLancamento?: 'receita' | 'despesa') => {
    // Determinar o tipo de lançamento se não foi passado como parâmetro
    let tipo = tipoLancamento;
    if (!tipo) {
      // Se tipoLancamento está definido no objeto (usado no extrato)
      if (lancamento.tipoLancamento) {
        tipo = lancamento.tipoLancamento;
      } else {
        // Determinar pelo tipoLancamentoFinanceiroId: 1 = receita, 2 = despesa
        tipo = lancamento.tipoLancamentoFinanceiroId === 1 ? 'receita' : 'despesa';
      }
    }

    // Mapear status corretamente baseado no tipo de lançamento
    let status = 'open'; // Padrão para não efetivado
    if (lancamento.efetivado) {
      status = tipo === 'receita' ? 'received' : 'paid';
    }

    return {
      id: lancamento.id.toString(),
      description: lancamento.descricao,
      dueDate: lancamento.dataLancamento || lancamento.dataPagamento,
      amount: lancamento.valor,
      netAmount: lancamento.valor,
      status: status,
      supplier: lancamento.pessoa?.nome || lancamento.fornecedor?.descricao || '-',
      client: lancamento.pessoa?.nome || '-',
      location: lancamento.local?.nome || lancamento.local?.descricao || '-',
      account: {
        name: lancamento.conta?.descricao || '-',
        id: lancamento.conta?.id
      },
      category: {
        id: lancamento.categoriaLancamentoFinanceiro?.id,
        name: lancamento.categoriaLancamentoFinanceiro?.descricao
      },
      tipoLancamento: lancamento.tipoLancamento // Para diferenciação no extrato
    };
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  // Filter accounts by month and advanced filters
  const filterAccounts = (accountsList: any[]) => {
    return accountsList.filter(account => {
      const accountDate = new Date(account.dueDate);
      
      // Month filter
      if (accountDate.getMonth() !== currentMonth || accountDate.getFullYear() !== currentYear) {
        return false;
      }
      
      // Advanced filters
      if (advancedFilters.category && account.category?.id !== advancedFilters.category) {
        return false;
      }
      
      if (advancedFilters.location && account.location !== advancedFilters.location) {
        return false;
      }
      
      if (advancedFilters.status && account.status !== advancedFilters.status) {
        return false;
      }
      
      if (advancedFilters.account && account.account?.id !== advancedFilters.account) {
        return false;
      }
      
      if (advancedFilters.supplier) {
        const supplierField = activeTab === 'pagar' ? account.supplier : account.client;
        if (!supplierField?.toLowerCase().includes(advancedFilters.supplier.toLowerCase())) {
          return false;
        }
      }
      
      if (advancedFilters.description) {
        if (!account.description?.toLowerCase().includes(advancedFilters.description.toLowerCase())) {
          return false;
        }
      }
      
      const accountAmount = activeTab === 'pagar' ? account.amount : (account.netAmount || account.amount);
      
      if (advancedFilters.minAmount && accountAmount < advancedFilters.minAmount) {
        return false;
      }
      
      if (advancedFilters.maxAmount && accountAmount > advancedFilters.maxAmount) {
        return false;
      }
      
      return true;
    });
  };

  // Mapear e filtrar dados dos novos lançamentos
  const dadosDespesasMapeados = despesas.map(lancamento => mapearLancamentoParaTabela(lancamento, 'despesa'));
  const dadosReceitasMapeados = receitas.map(lancamento => mapearLancamentoParaTabela(lancamento, 'receita'));

  const filteredPayableAccounts = filterAccounts(dadosDespesasMapeados);
  const filteredReceivableAccounts = filterAccounts(dadosReceitasMapeados);

  // Reset page when tab, month, filters, or page size change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, currentMonth, currentYear, advancedFilters, pageSize]);

  // Pagination
  const getCurrentPageData = (data: any[]) => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };

  const getTotalPages = (data: any[]) => Math.ceil(data.length / pageSize);

  // Calculate totals
  const calculateTotals = (accountsList: any[], type: 'payable' | 'receivable') => {
    const paid = accountsList.filter(acc => acc.status === (type === 'payable' ? 'paid' : 'received'));
    const open = accountsList.filter(acc => acc.status === 'open');
    const overdue = accountsList.filter(acc => acc.status === 'overdue');
    const selected = accountsList.filter(acc => selectedIds.includes(acc.id));

    const getAmount = (acc: any) => type === 'payable' ? acc.amount : (acc.netAmount || acc.amount);

    return {
      selected: selected.reduce((sum, acc) => sum + getAmount(acc), 0),
      paid: paid.reduce((sum, acc) => sum + getAmount(acc), 0),
      open: open.reduce((sum, acc) => sum + getAmount(acc), 0),
      overdue: overdue.reduce((sum, acc) => sum + getAmount(acc), 0),
      total: accountsList.reduce((sum, acc) => sum + getAmount(acc), 0),
    };
  };

  // Determinar dados atuais baseado na aba ativa
  const currentData = useMemo(() => {
    if (activeTab === 'pagar') {
      return despesas.map(lancamento => mapearLancamentoParaTabela(lancamento, 'despesa'));
    } else if (activeTab === 'receber') {
      return receitas.map(lancamento => mapearLancamentoParaTabela(lancamento, 'receita'));
    } else {
      // Para o extrato, não passamos o tipo pois ele já vem no objeto
      return extrato.map(lancamento => mapearLancamentoParaTabela(lancamento));
    }
  }, [activeTab, despesas, receitas, extrato]);
  const paginatedData = getCurrentPageData(currentData);
  const totalPages = getTotalPages(currentData);
  const totals = calculateTotals(currentData, activeTab === 'pagar' ? 'payable' : 'receivable');

  // Utility functions
  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  };

  const getStatusBadge = (account: any, type: 'payable' | 'receivable') => {
    const statusLabel = type === 'payable' ? 'Pago' : 'Recebido';
    const statusValue = type === 'payable' ? 'paid' : 'received';
    
    if (account.status === statusValue) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 text-xs px-2 py-0">
          <Check className="w-3 h-3 mr-1" />
          {statusLabel}
        </Badge>
      );
    }

    if (account.status === 'overdue') {
      return (
        <Badge variant="destructive" className="text-xs px-2 py-0">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Vencido
        </Badge>
      );
    }

    return (
      <Badge variant="secondary" className="text-xs px-2 py-0">
        <Calendar className="w-3 h-3 mr-1" />
        Em Aberto
      </Badge>
    );
  };

  const handleSelectItem = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(i => i !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedIds.length === paginatedData.length && paginatedData.length > 0) {
      setSelectedIds([]);
    } else {
      setSelectedIds(paginatedData.map(item => item.id));
    }
  };

  const handleMarkAsPaid = async (id: string) => {
    try {
      // Usar a nova função de alteração de status
      await alterarStatusEfetivado(id, true);

      // Recarregar dados
      await carregarDados();

      toast({
        title: 'Status atualizado',
        description: 'Item marcado como efetivado.',
      });
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkAsUnpaid = async (id: string) => {
    try {
      // Usar a nova função de alteração de status
      await alterarStatusEfetivado(id, false);

      // Recarregar dados
      await carregarDados();

      toast({
        title: 'Status atualizado',
        description: 'Item marcado como não efetivado.',
      });
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleClonarLancamento = async (id: string) => {
    try {
      console.log('🔍 Frontend - Clonando lançamento:', id);

      const lancamentoClonado = await clonarLancamento(id);

      // Abrir modal de criação com dados preenchidos
      if (lancamentoClonado.tipoLancamentoFinanceiroId === 1) {
        // É uma receita
        setReceitaClonada(lancamentoClonado);
        setIsCreateReceitaOpen(true);
      } else {
        // É uma despesa  
        setDespesaClonada(lancamentoClonado);
        setIsCreateDespesaOpen(true);
      }

      toast({
        title: 'Lançamento clonado',
        description: 'Os dados foram carregados no formulário. Ajuste conforme necessário e salve.',
      });
    } catch (error) {
      console.error('Erro ao clonar lançamento:', error);
      toast({
        title: 'Erro',
        description: 'Não foi possível clonar o lançamento.',
        variant: 'destructive',
      });
    }
  };



  const handleStatusChange = async (status: 'open' | 'paid' | 'received' | 'overdue') => {
    try {
      if (activeTab === 'pagar') {
        await updateMultiplePayableStatus(selectedIds, status as 'open' | 'paid' | 'overdue');
      } else {
        await updateMultipleReceivableStatus(selectedIds, status as 'open' | 'received' | 'overdue');
      }
      setSelectedIds([]);
      
      const statusLabels = {
        'open': 'Em Aberto',
        'paid': 'Pago',
        'received': 'Recebido',
        'overdue': 'Vencido'
      };

      toast({
        title: 'Status atualizado',
        description: `${selectedIds.length} item(s) marcado(s) como ${statusLabels[status]}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar os status.',
        variant: 'destructive',
      });
    }
  };

  const handleBulkDelete = async () => {
    try {
      if (activeTab === 'pagar') {
        await deleteMultiplePayableAccounts(selectedIds);
      } else {
        await deleteMultipleReceivableAccounts(selectedIds);
      }
      setSelectedIds([]);
      
      toast({
        title: 'Itens excluídos',
        description: `${selectedIds.length} item(s) excluído(s) com sucesso.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir os itens.',
        variant: 'destructive',
      });
    }
  };

  const prepareExportData = (accounts: any[]) => {
    return accounts.map(account => ({
      Data: formatDate(account.dueDate),
      Descrição: account.description,
      [activeTab === 'pagar' ? 'Fornecedor' : 'Cliente']: activeTab === 'pagar' ? account.supplier : account.client,
      Local: account.location || '',
      Conta: account.account?.name || '',
      Valor: activeTab === 'pagar' ? account.amount : (account.netAmount || account.amount),
      Status: account.status === (activeTab === 'pagar' ? 'paid' : 'received') ? 
        (activeTab === 'pagar' ? 'Pago' : 'Recebido') : 
        account.status === 'overdue' ? 'Vencido' : 'Em Aberto',
      Categoria: account.category?.name || '',
    }));
  };

  const handleExport = () => {
    // Se há itens selecionados, exporta apenas os selecionados. Caso contrário, exporta todos
    const accountsToExport = selectedIds.length > 0 
      ? currentData.filter(account => selectedIds.includes(account.id))
      : currentData;
    
    const exportData = prepareExportData(accountsToExport);
    const filename = selectedIds.length > 0 
      ? `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-selecionadas-${MONTHS[currentMonth]}-${currentYear}`
      : `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-${MONTHS[currentMonth]}-${currentYear}`;
    
    if (exportToExcel(exportData, filename, activeTab === 'pagar' ? 'Contas a Pagar' : 'Contas a Receber')) {
      const description = selectedIds.length > 0 
        ? `${accountsToExport.length} item(s) selecionado(s) exportado(s) para Excel.`
        : `${accountsToExport.length} item(s) exportado(s) para Excel.`;
        
      toast({
        title: 'Exportação concluída',
        description,
      });
    } else {
      toast({
        title: 'Erro na exportação',
        description: 'Não foi possível exportar os dados.',
        variant: 'destructive',
      });
    }
  };



  const handleApplyFilters = (filters: AdvancedFiltersData) => {
    setAdvancedFilters(filters);
  };

  const handleClearFilters = () => {
    setAdvancedFilters({});
  };

  const hasActiveFilters = Object.keys(advancedFilters).some(key => 
    advancedFilters[key as keyof AdvancedFiltersData] !== undefined
  );

  return (
    <Layout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Compact Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">Financeiro</h1>
          </div>

          {/* Month Navigation */}
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <div className="text-lg font-semibold min-w-[100px] text-center">
              {MONTHS[currentMonth]}/{currentYear}
            </div>
            <Button variant="outline" size="sm" onClick={goToNextMonth}>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button 
              variant={hasActiveFilters ? "default" : "outline"} 
              size="sm" 
              className="gap-2"
              onClick={() => setIsAdvancedFiltersOpen(true)}
            >
              <Filter className="w-4 h-4" />
              Filtros Avançados
              {hasActiveFilters && (
                <span className="bg-white text-blue-600 text-xs rounded-full px-1.5 py-0.5 ml-1">
                  {Object.keys(advancedFilters).length}
                </span>
              )}
            </Button>
            <BulkActionsToolbar
              type={activeTab === 'pagar' ? 'payable' : 'receivable'}
              selectedCount={selectedIds.length}
              selectedData={currentData.filter(account => selectedIds.includes(account.id))}
              onStatusChange={handleStatusChange}
              onBulkDelete={handleBulkDelete}
              onExport={handleExport}
              disabled={lancamentosLoading}
            />
          </div>
        </div>

        {/* Compact Tabs */}
        <div className="px-6 py-2 border-b">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'pagar' | 'receber' | 'extrato')}>
            <div className="flex items-center justify-between">
              <TabsList className="grid w-auto grid-cols-3">
                <TabsTrigger value="pagar" className="text-sm">Pagar</TabsTrigger>
                <TabsTrigger value="receber" className="text-sm">Receber</TabsTrigger>
                <TabsTrigger value="extrato" className="text-sm">Extrato</TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                {activeTab !== 'extrato' && (
                  <Button
                    size="sm"
                    onClick={() => activeTab === 'pagar' ? setIsCreateDespesaOpen(true) : setIsCreateReceitaOpen(true)}
                    className="gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Nova {activeTab === 'pagar' ? 'Despesa' : 'Receita'}
                  </Button>
                )}
              </div>
            </div>
          </Tabs>
        </div>

        {/* Table Container */}
        <div className="flex-1 flex flex-col px-6 py-2 overflow-hidden">
          <div className="rounded-md border flex-1 overflow-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow className="h-10">
                  <TableHead className="w-10">
                    <Checkbox 
                      checked={selectedIds.length === paginatedData.length && paginatedData.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="text-xs">Data</TableHead>
                  <TableHead className="text-xs">Descrição</TableHead>
                  <TableHead className="text-xs">{activeTab === 'pagar' ? 'Fornecedor' : 'Cliente'}</TableHead>
                  <TableHead className="text-xs">Local</TableHead>
                  <TableHead className="text-xs">Conta</TableHead>
                  <TableHead className="text-xs text-right">Valor</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  <TableHead className="text-xs w-32">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((item) => (
                  <TableRow key={item.id} className="h-12">
                    <TableCell>
                      <Checkbox 
                        checked={selectedIds.includes(item.id)}
                        onCheckedChange={() => handleSelectItem(item.id)}
                      />
                    </TableCell>
                    <TableCell className="text-sm">{formatDate(item.dueDate)}</TableCell>
                    <TableCell className="text-sm max-w-[200px]" title={item.description}>
                      <div className="flex items-center gap-2">
                        {activeTab === 'extrato' && (
                          <Badge
                            variant={item.tipoLancamento === 'receita' ? 'default' : 'destructive'}
                            className={`text-xs px-2 py-0.5 ${
                              item.tipoLancamento === 'receita'
                                ? 'bg-green-100 text-green-800 border-green-200'
                                : 'bg-red-100 text-red-800 border-red-200'
                            }`}
                          >
                            {item.tipoLancamento === 'receita' ? 'R' : 'D'}
                          </Badge>
                        )}
                        <span className="truncate">{item.description}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      {activeTab === 'pagar' ? item.supplier : item.client}
                    </TableCell>
                    <TableCell className="text-sm">{item.location || '-'}</TableCell>
                    <TableCell className="text-sm">{item.account?.name || '-'}</TableCell>
                    <TableCell className="text-sm text-right font-mono">
                      <span className={
                        activeTab === 'extrato'
                          ? item.tipoLancamento === 'receita'
                            ? 'text-green-600 font-semibold'
                            : 'text-red-600 font-semibold'
                          : ''
                      }>
                        {activeTab === 'extrato' && item.tipoLancamento === 'receita' && '+ '}
                        {activeTab === 'extrato' && item.tipoLancamento === 'despesa' && '- '}
                        {formatCurrency(activeTab === 'pagar' ? item.amount : (item.netAmount || item.amount))}
                      </span>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item, activeTab === 'pagar' ? 'payable' : 'receivable')}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {item.status === (activeTab === 'pagar' ? 'paid' : 'received') ? (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleMarkAsUnpaid(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        ) : (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleMarkAsPaid(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="w-4 h-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleClonarLancamento(item.id)}
                          className="h-8 w-8 p-0"
                          title="Clonar lançamento"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                        {activeTab !== 'extrato' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              if (activeTab === 'pagar') {
                                setEditingPayable(item);
                              } else {
                                setEditingReceivable(item);
                              }
                            }}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {/* Totals row right after the last record */}
                <TableRow>
                  <TableCell colSpan={9} className="border-t-2 bg-slate-50 p-4">
                    <div className="flex flex-wrap items-center justify-between gap-x-6 gap-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Selecionadas:</span>
                        <span className="font-mono font-semibold text-slate-900">R$ {formatCurrency(totals.selected)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">{activeTab === 'pagar' ? 'Pagas' : 'Recebidas'}:</span>
                        <span className="font-mono font-semibold text-green-600">R$ {formatCurrency(totals.paid)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">A {activeTab === 'pagar' ? 'Pagar' : 'Receber'}:</span>
                        <span className="font-mono font-semibold text-blue-600">R$ {formatCurrency(totals.open)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Vencidas:</span>
                        <span className="font-mono font-semibold text-red-600">R$ {formatCurrency(totals.overdue)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 border-l border-slate-300 pl-4 ml-2">
                        <span className="text-slate-800 font-semibold">Total:</span>
                        <span className="font-mono font-bold text-slate-900">R$ {formatCurrency(totals.total)}</span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-background border border-t-0 rounded-b-lg px-4 py-3 mt-0">
              <TablePagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                totalItems={currentData.length}
                onPageChange={setCurrentPage}
                onPageSizeChange={setPageSize}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <DespesaFormDialog
          open={isCreateDespesaOpen}
          onOpenChange={(open) => {
            setIsCreateDespesaOpen(open);
            if (!open) {
              setDespesaClonada(null); // Limpar dados clonados quando fechar o modal
            }
          }}
          despesa={despesaClonada}
          onSuccess={() => {
            carregarDados();
            console.log('Despesa criada com sucesso');
          }}
        />

        <ReceitaFormDialog
          open={isCreateReceitaOpen}
          onOpenChange={(open) => {
            setIsCreateReceitaOpen(open);
            if (!open) {
              setReceitaClonada(null); // Limpar dados clonados quando fechar o modal
            }
          }}
          receita={receitaClonada}
          onSuccess={() => {
            carregarDados();
            console.log('Receita criada com sucesso');
          }}
        />

        <AccountPayableFormDialog
          open={!!editingPayable}
          onOpenChange={(open) => !open && setEditingPayable(null)}
          account={editingPayable}
          onSuccess={() => {}}
        />

        <AccountReceivableFormDialog
          open={!!editingReceivable}
          onOpenChange={(open) => !open && setEditingReceivable(null)}
          account={editingReceivable}
          onSuccess={() => {}}
        />

        <AdvancedFiltersModal
          open={isAdvancedFiltersOpen}
          onOpenChange={setIsAdvancedFiltersOpen}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          currentFilters={advancedFilters}
          activeTab={activeTab === 'extrato' ? 'pagar' : activeTab}
        />
      </div>
    </Layout>
  );
} 